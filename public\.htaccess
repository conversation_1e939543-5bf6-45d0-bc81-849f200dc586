# 启用重写引擎
RewriteEngine On
Options -MultiViews

# 设置基础URL（如果应用部署在子目录中，请相应修改）
RewriteBase /

# 排除 /api/ 路径，避免重写规则影响 API 请求
RewriteCond %{REQUEST_URI} !^/api/

# 允许从这些目录直接访问文件
RewriteCond %{REQUEST_URI} !^/assets/
RewriteCond %{REQUEST_URI} !^/images/
RewriteCond %{REQUEST_URI} !^/logo.png

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# 将所有其他请求重定向到index.html
RewriteRule . /index.html [L]

# 设置一些缓存控制头
<IfModule mod_headers.c>
  # 禁止缓存index.html，确保始终获取最新版本
  <FilesMatch "index\.html$">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "0"
  </FilesMatch>

  # 为静态资源设置长期缓存
  <FilesMatch "\.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$">
    Header set Cache-Control "max-age=31536000, public"
  </FilesMatch>
</IfModule>
