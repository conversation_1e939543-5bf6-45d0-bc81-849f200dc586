import { get } from './api';

// Dashboard API 响应类型定义
export interface PracticeProgressResponse {
  total_assigned: number;
  completed: number;
  in_progress: number;
  pending: number;
}

export interface RecentActivity {
  e_id: number; // 练习ID
  e_title: string; // 练习标题
  e_type: number; // 练习类型
  e_pic?: string | null; // 练习图片URL
  w_id?: number | null; // 作业单ID
  s_id?: number | null; // 场景ID
  c_id: number; // 班级ID
  c_name: string; // 班级名称
  el_id?: number | null; // 练习情况ID
  el_status?: number | null; // 练习状态（0：待练习；1：练习中；2：已提交）
  el_btime?: string | null; // 开始练习时间
  el_stime?: string | null; // 提交练习时间
  el_utime?: string | null; // 上次更新时间
}

export interface RecentPracticeActivityResponse {
  activities: RecentActivity[];
}



export interface DailyTrend {
  date: string;
  completed_count: number;
}

export interface LearningTrendResponse {
  daily_trends: DailyTrend[];
}

// Dashboard API 服务
export const dashboardService = {
  // 获取整体练习进度
  getPracticeProgress: (): Promise<PracticeProgressResponse> => {
    return get<PracticeProgressResponse>('/dashboard/practice-progress');
  },

  // 获取最近练习活动
  getRecentActivity: (): Promise<RecentPracticeActivityResponse> => {
    return get<RecentPracticeActivityResponse>('/dashboard/recent-activity');
  },



  // 获取学习趋势
  getLearningTrend: (): Promise<LearningTrendResponse> => {
    return get<LearningTrendResponse>('/dashboard/learning-trend');
  },
};