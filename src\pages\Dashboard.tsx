import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Typography, Progress, List, Row, Col, Spin, message } from 'antd';
import { useAuthStore } from '../utils/authStore';
import { MainLayout } from '../layouts';
import { dashboardService, type PracticeProgressResponse, type RecentActivity, type DailyTrend } from '../services/dashboard';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { user } = useAuthStore();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [practiceProgress, setPracticeProgress] = useState<PracticeProgressResponse | null>(null);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  
  // 处理练习项目点击
  const handlePracticeClick = (activity: RecentActivity) => {
    if (activity.e_type === 1) {
      // 作业单类型，跳转到Worksheet页面
      if (activity.w_id) {
        navigate(`/worksheet/${activity.w_id}/${activity.c_id}`);
      }
    } else if (activity.e_type === 2) {
      // 角色扮演类型，跳转到Scene页面
      if (activity.s_id) {
        navigate(`/scene/${activity.s_id}/${activity.c_id}`);
      }
    }
  };
  const [learningTrend, setLearningTrend] = useState<DailyTrend[]>([]);

  // 获取状态文本
  const getStatusText = (status: number) => {
    switch (status) {
      case 0: return '待练习';
      case 1: return '练习中';
      case 2: return '已完成';
      default: return '未知';
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: number) => {
    switch (status) {
      case 0: return '#faad14';
      case 1: return '#1890ff';
      case 2: return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  // 加载Dashboard数据
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [progressData, activityData, trendData] = await Promise.all([
        dashboardService.getPracticeProgress(),
        dashboardService.getRecentActivity(),
        dashboardService.getLearningTrend(),
      ]);

      setPracticeProgress(progressData);
      setRecentActivities(activityData.activities || []);
      setLearningTrend(trendData.daily_trends || []);
    } catch (error) {
      console.error('加载Dashboard数据失败:', error);
      message.error('加载数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  if (loading) {
    return (
      <MainLayout>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '60vh',
          background: 'linear-gradient(135deg, #21808d 0%, #2a9aa8 100%)',
          borderRadius: '16px',
          margin: '24px'
        }}>
          <div style={{ textAlign: 'center', color: 'white' }}>
            <Spin size="large" style={{ color: 'white' }} />
            <div style={{ marginTop: '16px', fontSize: '16px' }}>正在加载您的学习数据...</div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div style={{ 
        maxWidth: 1400, 
        margin: '0 auto', 
        padding: '24px',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
      }}>
        {/* 欢迎信息 */}
        <div style={{ 
          marginBottom: '32px',
          padding: '32px',
          background: 'linear-gradient(135deg, #21808d 0%, #2a9aa8 100%)',
          borderRadius: '20px',
          color: 'white',
          boxShadow: '0 20px 40px rgba(33, 128, 141, 0.3)',
          position: 'relative',
          overflow: 'hidden'
        }}>
          <div style={{
            position: 'absolute',
            top: '-50%',
            right: '-10%',
            width: '200px',
            height: '200px',
            background: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '50%',
            filter: 'blur(40px)'
          }} />
          <div style={{
            position: 'absolute',
            bottom: '-30%',
            left: '-5%',
            width: '150px',
            height: '150px',
            background: 'rgba(255, 255, 255, 0.05)',
            borderRadius: '50%',
            filter: 'blur(30px)'
          }} />
          <div style={{ position: 'relative', zIndex: 1 }}>
            <Title level={1} style={{ 
              color: 'white', 
              marginBottom: '12px',
              fontSize: '36px',
              fontWeight: 700,
              textShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              欢迎回来，{user?.name || user?.username}！
            </Title>
            <Text style={{ 
              fontSize: '18px', 
              color: 'rgba(255, 255, 255, 0.9)',
              fontWeight: 300
            }}>
              探索您的学习宇宙，每一次练习都是向前的一步 🚀
            </Text>
          </div>
        </div>

        {/* 学习进度总览 - 融合统计信息 */}
        <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
          <Col span={24}>
            <Card 
              title={
                <span style={{ 
                  fontSize: '20px', 
                  fontWeight: 700,
                  color: '#21808d'
                }}>
                  学习进度总览
                </span>
              } 
              style={{ 
                borderRadius: '20px',
                border: 'none',
                boxShadow: '0 8px 32px rgba(33, 128, 141, 0.15)',
                background: 'white'
              }}
            >
              <Row gutter={[32, 32]} align="middle">
                {/* 左侧统计数据 */}
                <Col xs={24} lg={12}>
                  <Row gutter={[24, 24]}>
                    <Col xs={12} sm={6}>
                      <div style={{ 
                        textAlign: 'center',
                        padding: '20px',
                        borderRadius: '16px',
                        background: 'linear-gradient(135deg, #21808d 0%, #2a9aa8 100%)',
                        color: 'white',
                        boxShadow: '0 4px 16px rgba(33, 128, 141, 0.3)'
                      }}>
                        <div style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '8px' }}>
                          {practiceProgress?.total_assigned || 0}
                        </div>
                        <div style={{ fontSize: '12px', opacity: 0.9 }}>练习总数</div>
                      </div>
                    </Col>
                    <Col xs={12} sm={6}>
                      <div style={{ 
                        textAlign: 'center',
                        padding: '20px',
                        borderRadius: '16px',
                        background: 'linear-gradient(135deg, #52c41a 0%, #73d13d 100%)',
                        color: 'white',
                        boxShadow: '0 4px 16px rgba(82, 196, 26, 0.3)'
                      }}>
                        <div style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '8px' }}>
                          {practiceProgress?.completed || 0}
                        </div>
                        <div style={{ fontSize: '12px', opacity: 0.9 }}>已完成</div>
                      </div>
                    </Col>
                    <Col xs={12} sm={6}>
                      <div style={{ 
                        textAlign: 'center',
                        padding: '20px',
                        borderRadius: '16px',
                        background: 'linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)',
                        color: 'white',
                        boxShadow: '0 4px 16px rgba(24, 144, 255, 0.3)'
                      }}>
                        <div style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '8px' }}>
                          {practiceProgress?.in_progress || 0}
                        </div>
                        <div style={{ fontSize: '12px', opacity: 0.9 }}>练习中</div>
                      </div>
                    </Col>
                    <Col xs={12} sm={6}>
                      <div style={{ 
                        textAlign: 'center',
                        padding: '20px',
                        borderRadius: '16px',
                        background: 'linear-gradient(135deg, #faad14 0%, #ffc53d 100%)',
                        color: 'white',
                        boxShadow: '0 4px 16px rgba(250, 173, 20, 0.3)'
                      }}>
                        <div style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '8px' }}>
                          {practiceProgress?.pending || 0}
                        </div>
                        <div style={{ fontSize: '12px', opacity: 0.9 }}>未练习</div>
                      </div>
                    </Col>
                  </Row>
                </Col>
                
                {/* 右侧环形进度图 */}
                <Col xs={24} lg={12}>
                  <div style={{ display: 'flex', justifyContent: 'center', padding: '20px 0' }}>
                    <div style={{ textAlign: 'center' }}>
                      <Progress
                        type="circle"
                        percent={practiceProgress ? Math.round((practiceProgress.completed / practiceProgress.total_assigned) * 100) : 0}
                        size={200}
                        strokeColor={{
                          '0%': '#21808d',
                          '50%': '#2a9aa8',
                          '100%': '#52c41a',
                        }}
                        strokeWidth={12}
                        format={(percent) => (
                          <div style={{ textAlign: 'center' }}>
                            <div style={{ 
                              fontSize: '32px', 
                              fontWeight: 'bold',
                              color: '#21808d'
                            }}>
                              {percent}%
                            </div>
                            <div style={{ fontSize: '14px', color: '#666', marginTop: '8px' }}>
                              完成进度
                            </div>
                          </div>
                        )}
                      />
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* 活动和趋势 */}
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={12}>
            <Card 
              title={
                <span style={{ 
                  fontSize: '18px', 
                  fontWeight: 600,
                  background: 'linear-gradient(135deg, #21808d 0%, #2a9aa8 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  最近练习活动
                </span>
              } 
              style={{ 
                borderRadius: '20px',
                border: 'none',
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                background: 'white',
                minHeight: '500px'
              }}
            >
              <div style={{ maxHeight: '400px', overflowY: 'auto', overflowX: 'hidden' }}>
                <List
                  dataSource={recentActivities}
                  renderItem={(item) => (
                    <List.Item 
                      style={{ 
                        padding: '16px',
                        borderBottom: '1px solid #f0f0f0',
                        transition: 'all 0.3s ease',
                        cursor: 'pointer',
                        overflow: 'hidden'
                      }}
                      onClick={() => handlePracticeClick(item)}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#f8fafc';
                        e.currentTarget.style.transform = 'translateX(4px)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.transform = 'translateX(0)';
                      }}
                    >
                      <List.Item.Meta
                        avatar={
                          item.e_pic ? (
                            <img 
                              src={item.e_pic} 
                              alt={item.e_title}
                              style={{ 
                                width: 56, 
                                height: 56, 
                                borderRadius: '12px', 
                                objectFit: 'cover',
                                boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                              }}
                            />
                          ) : (
                            <div 
                              style={{ 
                                width: 56, 
                                height: 56, 
                                borderRadius: '12px', 
                                background: 'linear-gradient(135deg, #21808d 0%, #2a9aa8 100%)',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '24px',
                                color: 'white',
                                boxShadow: '0 4px 12px rgba(33, 128, 141, 0.3)'
                              }}
                            >
                              {item.e_type === 1 ? '📝' : '🎭'}
                            </div>
                          )
                        }
                        title={
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <span style={{ fontWeight: 600, fontSize: '16px', color: '#1a1a1a' }}>
                              {item.e_title}
                            </span>
                            <span
                              style={{
                                color: getStatusColor(item.el_status || 0),
                                fontSize: '12px',
                                fontWeight: 600,
                                padding: '4px 12px',
                                borderRadius: '20px',
                                backgroundColor: `${getStatusColor(item.el_status || 0)}15`,
                                border: `1px solid ${getStatusColor(item.el_status || 0)}30`
                              }}
                            >
                              {getStatusText(item.el_status || 0)}
                            </span>
                          </div>
                        }
                        description={
                          <div>
                            <div style={{ color: '#666', marginBottom: '6px', fontSize: '14px' }}>
                              班级：{item.c_name}
                            </div>
                            <div style={{ fontSize: '12px', color: '#999' }}>
                              更新时间：{item.el_utime ? new Date(item.el_utime).toLocaleString() : '暂无'}
                            </div>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                  locale={{ emptyText: '暂无最近活动' }}
                />
              </div>
            </Card>
          </Col>
          
          <Col xs={24} lg={12}>
            <Card 
              title={
                <span style={{ 
                  fontSize: '18px', 
                  fontWeight: 600,
                  background: 'linear-gradient(135deg, #21808d 0%, #2a9aa8 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  学习趋势分析
                </span>
              } 
              style={{ 
                borderRadius: '20px',
                border: 'none',
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                background: 'white',
                minHeight: '500px'
              }}
            >
              <div style={{ padding: '20px 0' }}>
                <Text type="secondary" style={{ marginBottom: '30px', fontSize: '14px', display: 'block' }}>
                  最近10天完成练习数量趋势
                </Text>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'end', 
                  justifyContent: 'space-between', 
                  gap: '8px', 
                  padding: '20px',
                  background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
                  borderRadius: '16px',
                  minHeight: '300px'
                }}>
                  {learningTrend && learningTrend.length > 0 ? learningTrend.map((trend, index) => (
                    <div key={index} style={{ textAlign: 'center', flex: 1, minWidth: '40px' }}>
                      <div
                        style={{
                          height: `${Math.max(trend.completed_count * 40, 12)}px`,
                          marginBottom: '16px',
                          borderRadius: '8px 8px 0 0',
                          minHeight: '12px',
                          transition: 'all 0.3s ease',
                          background: trend.completed_count > 0 
                            ? 'linear-gradient(180deg, #21808d 0%, #2a9aa8 100%)'
                            : '#e2e8f0',
                          cursor: 'pointer',
                          boxShadow: trend.completed_count > 0 ? '0 4px 12px rgba(33, 128, 141, 0.3)' : 'none',
                          transform: 'scale(1)',
                        }}
                        title={`${new Date(trend.date).toLocaleDateString()}: ${trend.completed_count}个练习`}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'scale(1.05)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'scale(1)';
                        }}
                      />
                      <div style={{ 
                        fontSize: '14px', 
                        color: '#1a1a1a', 
                        fontWeight: 700, 
                        marginBottom: '6px' 
                      }}>
                        {trend.completed_count}
                      </div>
                      <div style={{ fontSize: '11px', color: '#666' }}>
                        {new Date(trend.date).toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })}
                      </div>
                    </div>
                  )) : (
                    <div style={{ 
                      textAlign: 'center', 
                      color: '#999', 
                      width: '100%', 
                      padding: '80px 0' 
                    }}>
                      <div style={{ fontSize: '64px', marginBottom: '20px' }}>📊</div>
                      <div style={{ fontSize: '16px', fontWeight: 500 }}>暂无学习趋势数据</div>
                      <div style={{ fontSize: '14px', marginTop: '8px', color: '#ccc' }}>
                        开始练习后这里将显示您的学习轨迹
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    </MainLayout>
  );
};

export default Dashboard;
